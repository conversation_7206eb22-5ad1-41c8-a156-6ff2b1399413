using UnityEngine;
using KinematicCharacterController;

namespace KinematicCharacterController.FPS
{
    public class SlidingSystem
    {
        private readonly FPSCharacterController _controller;
        private readonly KinematicCharacterMotor _motor;
        private readonly Transform _meshRoot;

        // Sliding parameters
        private readonly float _slideCapsuleHeight;
        private readonly float _slideSlopeAcceleration;
        private readonly float _slideFriction;
        private readonly float _minSlideSpeed;
        private readonly float _jumpUpSpeed;
        private readonly float _jumpScalableForwardSpeed;
        private readonly float _maxStableMoveSpeed;
        private readonly float _crouchSpeedMultiplier;
        private readonly Vector3 _gravity; // Store controller's custom gravity

        // Additional sliding parameters
        private readonly float _flatGroundFriction = 2.5f; // Higher friction on flat ground
        private readonly float _postSlopeFriction = 1.2f; // Friction after leaving a slope
        private readonly float _slopeAngleThreshold = 10f; // Angle that defines what we consider a slope
        private readonly float _speedMatchRate = 5f; // How quickly to match crouch walk speed
        private readonly float _slideControlMultiplier = 0.3f; // How much lateral control during slide
        private readonly float _slideControlSharpness = 15f; // How responsive the lateral control is
        private readonly float _initialSpeedBoost = 5f; // Flat speed boost in m/s
        private readonly float _minSpeedForBoost = 2f; // Minimum speed required to get the boost
        private readonly float _maxSlopeAccelerationFactor = 1.5f; // Maximum acceleration factor on steep slopes
        private readonly float _gravityMultiplier; // Increased gravity multiplier for slides
        private readonly float _airborneGravityMultiplier; // Even stronger gravity when airborne during slides

        // Improved momentum tracking
        private bool _wasOnSlope = false; // Track if we were just on a slope
        private bool _hasAppliedBoost = false; // Track if we've already applied the boost
        private float _currentSlopeAngle = 0f; // Track current slope angle for variable acceleration
        private Vector3 _currentVelocityOnPlane = Vector3.zero; // Store this for external access
        private float _airborneTime = 0f; // Track how long we've been in the air
        
        // Simple airborne state tracking for momentum preservation
        private bool _wasAirborne = false; // Track previous airborne state

        // Grace period for minimum speed check to prevent random slide stopping
        private float _belowMinSpeedTime = 0f;
        private readonly float _minSpeedGracePeriod = 0.3f; // Grace period before stopping slide

        // Public property to access current slide speed for FOV effects
        public float CurrentSlideSpeed => _currentVelocityOnPlane.magnitude;

        public SlidingSystem(FPSCharacterController controller, KinematicCharacterMotor motor, Transform meshRoot, 
            float slideCapsuleHeight, float slideSlopeAcceleration, float slideFriction, float minSlideSpeed,
            float jumpUpSpeed, float jumpScalableForwardSpeed)
        {
            _controller = controller;
            _motor = motor;
            _meshRoot = meshRoot;
            _slideCapsuleHeight = slideCapsuleHeight;
            _slideSlopeAcceleration = slideSlopeAcceleration;
            _slideFriction = slideFriction;
            _minSlideSpeed = minSlideSpeed;
            _jumpUpSpeed = jumpUpSpeed;
            _jumpScalableForwardSpeed = jumpScalableForwardSpeed;
            _gravity = controller.Gravity; // Store controller's custom gravity
            
            // Get gravity multipliers from controller
            _gravityMultiplier = controller.SlideGravityMultiplier;
            _airborneGravityMultiplier = controller.SlideAirborneGravityMultiplier;

            // Get these from the controller for consistent speeds
            _maxStableMoveSpeed = controller.MaxStableMoveSpeed;
            _crouchSpeedMultiplier = controller.CrouchSpeedMultiplier;
        }

        public void StartSliding()
        {
            // Force us into a "crouch" posture
            _controller.SetCrouchState(true, true);

            // Shorter capsule for sliding
            _motor.SetCapsuleDimensions(0.5f, _slideCapsuleHeight, _slideCapsuleHeight * 0.5f);
            if (_meshRoot != null)
            {
                _meshRoot.localScale = new Vector3(1f, 0.5f, 1f);
            }

            // Preserve current air momentum when starting slide
            Vector3 currentVelocity = _motor.BaseVelocity;
            Vector3 horizontalVel = Vector3.ProjectOnPlane(currentVelocity, _motor.CharacterUp);
            Vector3 verticalVel = Vector3.Project(currentVelocity, _motor.CharacterUp);

            // If we were airborne and have significant momentum, preserve it
            if (_wasAirborne && horizontalVel.magnitude > _minSpeedForBoost)
            {
                // Keep the current air momentum - this is what we want!
                Debug.Log($"Preserving air momentum: {horizontalVel.magnitude:F2} m/s from current velocity");
                // Don't apply additional boost when preserving air momentum
                _motor.BaseVelocity = currentVelocity;
                _hasAppliedBoost = true; // Mark as boosted to prevent double boost
            }
            else
            {
                // Normal slide start - apply boost if we have enough speed
                Vector3 newBaseVelocity = horizontalVel + verticalVel;
                
                if (!_hasAppliedBoost && horizontalVel.magnitude >= _minSpeedForBoost)
                {
                    Vector3 moveDir = horizontalVel.normalized;
                    newBaseVelocity = horizontalVel + (moveDir * _initialSpeedBoost) + verticalVel;
                    _hasAppliedBoost = true;
                    Debug.Log($"Applied slide boost: {_initialSpeedBoost} m/s");
                }
                
                _motor.BaseVelocity = newBaseVelocity;
            }

            // Optionally unground so we can dip (reduced time to prevent interference)
            _motor.ForceUnground(0.05f);
            _wasOnSlope = false;
            _currentSlopeAngle = 0f;
            _airborneTime = 0f;
        }

        public void StopSliding(bool keepCrouched)
        {
            _hasAppliedBoost = false; // Reset the boost flag when stopping slide
            // Only stand up if we're not supposed to keep crouching
            if (!keepCrouched)
            {
                _controller.SetCrouchState(false, false);
                // Restore tall capsule
                _motor.SetCapsuleDimensions(0.5f, 2f, 1f);
                if (_meshRoot != null)
                {
                    _meshRoot.localScale = new Vector3(1f, 1f, 1f);
                }
            }
        }

        public void UpdateVelocity(ref Vector3 currentVelocity, float deltaTime, Vector3 moveInputVector, bool jumpRequested, bool jumpConsumed)
        {
            // Track airborne state changes
            bool isCurrentlyAirborne = !_motor.GroundingStatus.FoundAnyGround;
            
            // Update airborne tracking
            if (isCurrentlyAirborne)
            {
                _airborneTime += deltaTime;
            }
            else
            {
                _airborneTime = 0f;
            }
            
            _wasAirborne = isCurrentlyAirborne;

            // Apply gravity - using controller's custom gravity instead of Physics.gravity
            if (_airborneTime > 0.3f)
            {
                // For extended falls, apply full controller gravity with increased multiplier
                currentVelocity += _gravity * _airborneGravityMultiplier * deltaTime;
            }
            else
            {
                // Normal sliding gravity - use same gravity as the rest of the character but with a multiplier
                currentVelocity += _gravity * _gravityMultiplier * deltaTime;
            }

            Vector3 up = _motor.CharacterUp;
            Vector3 velocityOnPlane = Vector3.ProjectOnPlane(currentVelocity, up);
            _currentVelocityOnPlane = velocityOnPlane; // Store for external access
            
            // Calculate right direction for lateral movement
            Vector3 forwardDir = velocityOnPlane.sqrMagnitude > 0.001f ? velocityOnPlane.normalized : _motor.CharacterForward;
            Vector3 rightDir = Vector3.Cross(up, forwardDir).normalized;

            if (_motor.GroundingStatus.FoundAnyGround)
            {
                float groundAngle = Vector3.Angle(up, _motor.GroundingStatus.GroundNormal);
                _currentSlopeAngle = groundAngle; // Store for external access
                bool isOnSlope = groundAngle > _slopeAngleThreshold;

                if (!_motor.GroundingStatus.IsStableOnGround || isOnSlope)
                {
                    // On a slope steep enough to slide
                    _wasOnSlope = true;
                    
                    // Calculate acceleration based on slope angle - steeper slopes accelerate faster
                    // Using sine function is appropriate here since gravity's effect increases with sine of angle
                    float slopeAccelerationFactor = Mathf.Sin(groundAngle * Mathf.Deg2Rad);
                    
                    // Increase acceleration on steeper slopes, up to a maximum factor
                    slopeAccelerationFactor = Mathf.Min(slopeAccelerationFactor * 1.5f, _maxSlopeAccelerationFactor);
                    
                    Vector3 slopeDir = Vector3.ProjectOnPlane(-up, _motor.GroundingStatus.GroundNormal).normalized;
                    currentVelocity += slopeDir * (_slideSlopeAcceleration * slopeAccelerationFactor * deltaTime);

                    // Add lateral control on slopes too
                    AddLateralControl(ref currentVelocity, moveInputVector, rightDir, deltaTime);
                }
                else
                {
                    // On flat ground or gentle slope
                    float currentSpeed = velocityOnPlane.magnitude;
                    float targetCrouchSpeed = _maxStableMoveSpeed * _crouchSpeedMultiplier;

                    if (currentSpeed > targetCrouchSpeed)
                    {
                        // Calculate friction based on recent slope history and angle
                        float frictionFactor;
                        if (_wasOnSlope)
                        {
                            // Just came off a slope - use post-slope friction
                            frictionFactor = _postSlopeFriction;
                        }
                        else
                        {
                            // On flat ground - friction depends on angle
                            // Less friction on small inclines
                            frictionFactor = Mathf.Lerp(_flatGroundFriction, 0.5f, _currentSlopeAngle / 45f);
                        }
                        
                        // Apply the calculated friction
                        velocityOnPlane *= (1f - (frictionFactor * deltaTime));

                        // Add lateral control while still sliding
                        AddLateralControl(ref velocityOnPlane, moveInputVector, rightDir, deltaTime);
                        currentVelocity = velocityOnPlane + Vector3.Project(currentVelocity, up);
                    }
                    else
                    {
                        // We're at or below crouch speed, match the crouch movement speed
                        if (moveInputVector.sqrMagnitude > 0)
                        {
                            // If there's input, try to match input direction at crouch speed
                            Vector3 targetVelocity = moveInputVector * targetCrouchSpeed;
                            velocityOnPlane = Vector3.Lerp(velocityOnPlane, targetVelocity, 1f - Mathf.Exp(-_speedMatchRate * deltaTime));
                        }
                        else
                        {
                            // No input, continue slowing down
                            velocityOnPlane *= (1f - (_flatGroundFriction * deltaTime));
                        }
                        currentVelocity = velocityOnPlane + Vector3.Project(currentVelocity, up);
                    }
                    
                    // Check if we should stop sliding with grace period
                    float speed = velocityOnPlane.magnitude;
                    if (!_wasOnSlope && speed < _minSlideSpeed)
                    {
                        _belowMinSpeedTime += deltaTime;
                        if (_belowMinSpeedTime > _minSpeedGracePeriod)
                        {
                            // Signal to revert to default, but keep crouched if still holding the button
                            _controller.TransitionToState(CharacterState.Default);
                            return;
                        }
                    }
                    else
                    {
                        // Reset timer when speed is above minimum
                        _belowMinSpeedTime = 0f;
                    }
                    
                    // If we were on a slope but now we're not, start normal deceleration
                    if (_wasOnSlope)
                    {
                        _wasOnSlope = false;
                    }
                }
            }
            else
            {
                // In air - NO FRICTION! This was causing the slowdown issue
                // Only apply lateral control for maneuvering
                float airControlMultiplier = Mathf.Lerp(1f, 0.5f, Mathf.Clamp01(_airborneTime / 1.0f));
                AddLateralControl(ref velocityOnPlane, moveInputVector, rightDir, deltaTime * airControlMultiplier);
                
                // Combine horizontal and vertical velocity without any friction reduction
                currentVelocity = velocityOnPlane + Vector3.Project(currentVelocity, up);
            }

            // if user tries to jump while sliding
            if (jumpRequested)
            {
                bool canActuallyJump = !jumpConsumed && _motor.GroundingStatus.IsStableOnGround;
                if (canActuallyJump)
                {
                    Vector3 jumpDirection = _motor.CharacterUp;
                    _motor.ForceUnground();
                    currentVelocity += (jumpDirection * _jumpUpSpeed)
                                       - Vector3.Project(currentVelocity, _motor.CharacterUp);
                    currentVelocity += (moveInputVector * _jumpScalableForwardSpeed);

                    _controller.ConsumeJump();
                    _controller.TransitionToState(CharacterState.Default);
                }
            }
        }

        private void AddLateralControl(ref Vector3 velocity, Vector3 moveInput, Vector3 rightDir, float deltaTime)
        {
            // Extract the lateral input
            float lateralInput = Vector3.Dot(moveInput, rightDir);
            
            // Calculate desired lateral velocity
            Vector3 lateralVel = rightDir * (lateralInput * _maxStableMoveSpeed * _slideControlMultiplier);
            
            // Current lateral velocity
            float currentLateralSpeed = Vector3.Dot(velocity, rightDir);
            Vector3 currentLateralVel = rightDir * currentLateralSpeed;
            
            // Smoothly interpolate to desired lateral velocity
            Vector3 newLateralVel = Vector3.Lerp(
                currentLateralVel,
                lateralVel,
                1f - Mathf.Exp(-_slideControlSharpness * deltaTime));
            
            // Remove old lateral velocity and add new
            velocity -= currentLateralVel;
            velocity += newLateralVel;
        }
    }
}