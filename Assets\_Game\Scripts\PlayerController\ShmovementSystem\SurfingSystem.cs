using UnityEngine;
using KinematicCharacterController;
using KinematicCharacterController.FPS;

public class SurfingSystem
{
    // Core dependencies
    private readonly FPSCharacterController _controller;
    private readonly KinematicCharacterMotor _motor;

    // Angle constraints: {_minSurfAngle} < surfAngle < {_maxSurfAngle} required for initiation
    private readonly float _minSurfAngle = 75f;
    private readonly float _maxSurfAngle = 89f;

    // Movement coefficients: velocity = base * _surfAcceleration + side * (_surfControlMultiplier * _lateralAccelerationMultiplier)
    private readonly float _surfAcceleration = 8f;
    private readonly float _surfControlMultiplifier = 0.8f;
    private readonly float _lateralAccelerationMultiplier = 1.5f;
    
    // Physics modifiers: affect base engine calculations
    private readonly float _surfGravityMultiplier = 0.5f;
    private readonly float _surfFriction = 0.03f;
    private readonly float _surfStickForce = 5f;
    private readonly float _surfStickDistance = 0.2f;
    
    // Velocity constraints: _minSurfSpeed < velocity < _maxSurfSpeed
    private readonly float _minSurfSpeed = 3f;
    private readonly float _maxSurfSpeed = 35f;
    private readonly float _minLateralSpeed = 5f;
    
    // Momentum coefficients: affect velocity preservation during state changes
    private readonly float _momentumPreservation = 0.95f;
    private readonly float _impactSpeedMultiplier = 0.9f;
    private readonly float _lookInfluence = 0.8f;
    
    // Force application constants
    private readonly float _sideBoostForce = 12f;
    private readonly float _jumpBoostForce = 7f;

    private readonly LayerMask _wallLayer;

    // State tracking
    private bool _isSurfing = false;
    private Vector3 _lastSurfNormal;
    private float _currentSurfSpeed;
    private Vector3 _surfDirection;
    private bool _debugMode = false;

    public bool IsSurfing => _isSurfing;

    public SurfingSystem(FPSCharacterController controller, KinematicCharacterMotor motor)
    {
        _controller = controller;
        _motor = motor;
        _wallLayer = LayerMask.GetMask("Wall");
    }

    public void UpdateVelocity(ref Vector3 currentVelocity, float deltaTime, Vector3 moveInputVector)
    {
        // Input normalization: x component preserved, y zeroed during active surf
        if (_isSurfing)
        {
            moveInputVector = new Vector3(moveInputVector.x, 0f, 0f);
        }

        if (!_motor.GroundingStatus.FoundAnyGround)
        {
            _isSurfing = false;
            return;
        }

        if ((_wallLayer.value & (1 << _motor.GroundingStatus.GroundCollider.gameObject.layer)) == 0)
        {
            _isSurfing = false;
            return;
        }

        Vector3 surfNormal = _motor.GroundingStatus.GroundNormal;
        float surfAngle = Vector3.Angle(Vector3.up, surfNormal);

        if (_debugMode)
        {
            LogDebugInfo(surfAngle, surfNormal);
        }

        if (surfAngle >= _minSurfAngle && surfAngle <= _maxSurfAngle)
        {
            if (!_isSurfing)
            {
                StartSurfing(currentVelocity, surfNormal);
            }
            UpdateSurfing(ref currentVelocity, deltaTime, moveInputVector, surfNormal);
        }
        else
        {
            _isSurfing = false;
        }

        _lastSurfNormal = surfNormal;
    }

    private void StartSurfing(Vector3 currentVelocity, Vector3 surfNormal)
    {
        _isSurfing = true;

        // Initial velocity projection: removes normal component, preserves tangential
        Vector3 parallelVelocity = Vector3.ProjectOnPlane(currentVelocity, surfNormal);
        _currentSurfSpeed = Mathf.Max(parallelVelocity.magnitude * _impactSpeedMultiplier, _minSurfSpeed);
        
        _surfDirection = parallelVelocity.normalized;
        if (_surfDirection == Vector3.zero)
        {
            Vector3 lookDir = _motor.TransientRotation * Vector3.forward;
            _surfDirection = Vector3.ProjectOnPlane(lookDir, surfNormal).normalized;
        }

        _motor.ForceUnground(0.1f);
    }

    private void UpdateSurfing(ref Vector3 currentVelocity, float deltaTime, Vector3 moveInputVector, Vector3 surfNormal)
    {
        // Direction vectors for movement calculation
        Vector3 lookDir = _motor.TransientRotation * Vector3.forward;
        Vector3 surfLookDir = Vector3.ProjectOnPlane(lookDir, surfNormal).normalized;
        Vector3 surfRight = Vector3.Cross(surfNormal, Vector3.up).normalized;

        float rightInput = Vector3.Dot(moveInputVector, Vector3.right);

        // Lateral movement: momentum preservation + direction change
        if (Mathf.Abs(rightInput) > 0.1f)
        {
            Vector3 preservedMomentum = _surfDirection * _currentSurfSpeed * _momentumPreservation;
            Vector3 targetDirection = Vector3.ProjectOnPlane(surfLookDir + (surfRight * rightInput * _lateralAccelerationMultiplier), surfNormal).normalized;
            _surfDirection = Vector3.Lerp(_surfDirection, targetDirection, deltaTime * _surfAcceleration);

            float sideBoost = _sideBoostForce * Mathf.Abs(rightInput);
            _currentSurfSpeed = Mathf.Max(_currentSurfSpeed, _minLateralSpeed);
            _currentSurfSpeed += sideBoost * deltaTime;
        }

        // Velocity composition
        Vector3 baseVelocity = _surfDirection * _currentSurfSpeed;
        Vector3 controlVelocity = surfRight * rightInput * _surfControlMultiplifier * _currentSurfSpeed;
        Vector3 targetVelocity = baseVelocity + controlVelocity;

        targetVelocity += Physics.gravity * _surfGravityMultiplier * deltaTime;
        targetVelocity *= (1f - (_surfFriction * deltaTime));

        // Wall adhesion
        Vector3 toSurface = _motor.GroundingStatus.GroundPoint - _motor.TransientPosition;
        float surfaceDistance = Vector3.Dot(toSurface, surfNormal);
        if (Mathf.Abs(surfaceDistance) > _surfStickDistance)
        {
            targetVelocity += surfNormal * ((surfaceDistance - _surfStickDistance) * _surfStickForce);
        }

        _currentSurfSpeed = Mathf.Clamp(targetVelocity.magnitude, _minSurfSpeed, _maxSurfSpeed);
        currentVelocity = targetVelocity;

        if (_currentSurfSpeed < _minSurfSpeed)
        {
            _isSurfing = false;
        }
    }

    public bool HandleJump(ref Vector3 currentVelocity)
    {
        if (!_isSurfing)
            return false;

        currentVelocity += Vector3.up * _jumpBoostForce;
        currentVelocity *= _momentumPreservation;
        
        _isSurfing = false;
        return true;
    }

    private void LogDebugInfo(float surfAngle, Vector3 surfNormal)
    {
        Debug.Log($"Surface Angle: {surfAngle}, Current Speed: {_currentSurfSpeed}, Is Surfing: {_isSurfing}");
        Debug.DrawRay(_motor.TransientPosition, surfNormal * 2f, Color.red);
        Debug.DrawRay(_motor.TransientPosition, Vector3.up * 2f, Color.green);
    }
}