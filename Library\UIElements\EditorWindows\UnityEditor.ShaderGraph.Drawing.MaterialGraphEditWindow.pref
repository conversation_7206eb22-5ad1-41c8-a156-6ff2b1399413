%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12386, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Keys:
  - __PanelContainer__rootVisualContainer__43a96adfafbacc14291a8e22e1081e54__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedViewTransform
  - __PanelContainer__rootVisualContainer__43a96adfafbacc14291a8e22e1081e54__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedSelection
  - __PanelContainer__rootVisualContainer__1bc46135972b7b34ea601f2fc5bdb55d__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedViewTransform
  - __PanelContainer__rootVisualContainer__1bc46135972b7b34ea601f2fc5bdb55d__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedSelection
  - __PanelContainer__rootVisualContainer__31c75348c6538d447a918cbc5ec8c202__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedViewTransform
  - __PanelContainer__rootVisualContainer__31c75348c6538d447a918cbc5ec8c202__MaterialGraphView__UnityEditor.Experimental.GraphView.GraphView+PersistedSelection
  m_Values:
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{"position":{"x":1908.0,"y":352.0,"z":0.0},"scale":{"x":0.29659736156463625,"y":0.29659736156463625,"z":1.0}}'
  - '{"m_Version":0,"m_SelectedElementsArray":[]}'
