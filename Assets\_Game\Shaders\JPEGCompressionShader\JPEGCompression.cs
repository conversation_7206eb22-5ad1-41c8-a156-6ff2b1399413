using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.HighDefinition;
using System;
using UnityEngine.Experimental.Rendering;

[Serializable, VolumeComponentMenu("Post-processing/Custom/JPEGCompression")]
public sealed class JPEGCompression : CustomPostProcessVolumeComponent, IPostProcessComponent
{
    [Header("Compute Shader - Insert Compute Shader")]
    [Tooltip("Insert Compute Shader")]
    public ComputeShaderParameter JPEGComputeShaderParameter = new ComputeShaderParameter(null);
    
    [Header("Spatial Compression")]
    [Tooltip("Use Spatial Compression")]
    public BoolParameter useSpatialCompression = new BoolParameter (true);
    
    [Tooltip("Updates on Play")]
    public IntParameter screenDownsampling = new IntParameter (0);
    
    [Tooltip("Updates on Play")]
    public BoolParameter usePointFiltering = new BoolParameter (false);
    
    [Range(0.0f, 2.0f), Tooltip("Compression Threshold")]
    public FloatParameter compressionThreshold = new FloatParameter (0.0f);
    
    [Tooltip("Performance Mode")]
    public ClampedIntParameter fastPerformanceMode = new ClampedIntParameter(0,0,1);
    
    [Header("Temporal Compression (Playmode Only)")]
    
    [Tooltip("Use Temporal Compression")]
    public BoolParameter useTemporalCompression = new BoolParameter (false);
    
    [Tooltip("Use I-Frames")]
    public BoolParameter useIFrames = new BoolParameter (true);
    
    [Tooltip("Number of predicted frames")]
    public IntParameter numBFrames = new IntParameter (8);
    
    [Range(0.0f, 1.0f), Tooltip("Bitrate")]
    public FloatParameter bitrate = new FloatParameter (1.0f);
    
    [Range(0.0f, 0.95f)]
    public FloatParameter bitrateArtifacts = new FloatParameter (0.0f);
    //--------------------------------------------------------------------------------------------
    
    private RenderTargetIdentifier motionFrameIdentifier;
    private RenderTexture motionFrame;
    
    private RenderTargetIdentifier lastFrameIdentifier;
    private RenderTexture lastFrame;
    
    private Material motionMaterial;
    private Material bypassMaterial;
    
    private Vector2Int dimensions;
    
    private int frameIndex;
    private bool lastUsePointFiltering;

    public bool IsActive() => JPEGComputeShaderParameter.value != null;

    public RenderTexture motionRenderOutTexture;

    RTHandle lastFromCamera;
    
    // Do not forget to add this post process in the Custom Post Process Orders list (Project Settings > Graphics > HDRP Global Settings).
    public override CustomPostProcessInjectionPoint injectionPoint => CustomPostProcessInjectionPoint.AfterPostProcess;
    
    //--------------------------------------------------------------------------------------------

    public override void Setup()
    {
        // HDRP handles motion vectors via Frame Settings; avoid relying on Camera.main here.
        // Only create materials now; allocate render targets lazily in Render when we know the destination size.
        var motionShader = Shader.Find("Hidden/Shader/MotionPostProcess");
        var bypassShader = Shader.Find("Hidden/Shader/bypassShader");

        if (motionShader == null || bypassShader == null)
        {
            Debug.LogError("JPEGCompression: Required shaders not found. Ensure 'MotionPostProcess.shader' and 'bypassShader.shader' are included in the project.");
            return;
        }

        motionMaterial = new Material(motionShader);
        bypassMaterial = new Material(bypassShader);

        frameIndex = 0;
        dimensions = new Vector2Int(0, 0);
        lastUsePointFiltering = usePointFiltering.value;
    }

    public override void Render(CommandBuffer cmd, HDCamera camera, RTHandle source, RTHandle destination)
    {
        // Ensure render targets exist and match current destination size (considering downsampling and filtering settings)
        int downSamplingRate = Mathf.Max(1, (screenDownsampling.value + 1));
        int targetWidth = Mathf.Max(1, destination.rt.width / downSamplingRate);
        int targetHeight = Mathf.Max(1, destination.rt.height / downSamplingRate);

        if (motionFrame == null || lastFrame == null || dimensions.x != targetWidth || dimensions.y != targetHeight)
        {
            // Release old targets if any
            if (motionFrame != null)
            {
                motionFrame.Release();
            }
            if (lastFrame != null)
            {
                lastFrame.Release();
            }

            dimensions = new Vector2Int(targetWidth, targetHeight);

            motionFrame = new RenderTexture(dimensions.x, dimensions.y, 16, GraphicsFormat.R16G16B16A16_SNorm)
            {
                enableRandomWrite = true,
                filterMode = usePointFiltering.value ? FilterMode.Point : FilterMode.Bilinear
            };
            motionFrame.Create();
            motionFrameIdentifier = new RenderTargetIdentifier(motionFrame);

            lastFrame = new RenderTexture(dimensions.x, dimensions.y, 16)
            {
                filterMode = usePointFiltering.value ? FilterMode.Point : FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Repeat
            };
            lastFrame.Create();
            lastFrameIdentifier = new RenderTargetIdentifier(lastFrame);
        }
        else if (lastUsePointFiltering != usePointFiltering.value)
        {
            // Update filter mode live when the parameter changes
            var newFilter = usePointFiltering.value ? FilterMode.Point : FilterMode.Bilinear;
            if (motionFrame != null) motionFrame.filterMode = newFilter;
            if (lastFrame != null) lastFrame.filterMode = newFilter;
        }
        lastUsePointFiltering = usePointFiltering.value;

        lastFromCamera = camera.GetPreviousFrameRT(1);
        
        if (motionMaterial != null)
        {
            cmd.Blit(motionFrame, motionFrameIdentifier, motionMaterial);
        }
        
        var JPEGComputeShader = JPEGComputeShaderParameter.value;
        if (JPEGComputeShader == null)
        {
            // Graceful fallback: just copy source to destination
            cmd.Blit(source, destination);
            return;
        }
        var mainKernel = JPEGComputeShader.FindKernel("CSMain");
        JPEGComputeShader.GetKernelThreadGroupSizes(mainKernel, out uint xGroupSize, out uint yGroupSize, out _);
        cmd.SetComputeTextureParam(JPEGComputeShader, mainKernel, "Input", source.nameID);
        cmd.SetComputeTextureParam(JPEGComputeShader, mainKernel, "Result", destination.nameID);
        cmd.SetComputeTextureParam(JPEGComputeShader, mainKernel, "Last", lastFrameIdentifier);
        cmd.SetComputeTextureParam(JPEGComputeShader, mainKernel, "Motion", motionFrame);
        
        cmd.SetComputeIntParam(JPEGComputeShader, "FastPerfomanceMode", fastPerformanceMode.value);
        cmd.SetComputeFloatParam(JPEGComputeShader, "CompressionThreshold", compressionThreshold.value);
        
        cmd.SetComputeIntParam(JPEGComputeShader, "UseSpacial", Convert.ToInt32(useSpatialCompression.value));
        
        cmd.SetComputeIntParam(JPEGComputeShader, "UseTemporal", Convert.ToInt32(useTemporalCompression.value));
        cmd.SetComputeIntParam(JPEGComputeShader, "UseIFrames", Convert.ToInt32(useIFrames.value));
        
        cmd.SetComputeFloatParam(JPEGComputeShader, "Bitrate", bitrate.value);
        cmd.SetComputeFloatParam(JPEGComputeShader, "BitrateArtifacts", bitrateArtifacts.value);
        cmd.SetComputeIntParam(JPEGComputeShader, "ResultWidth", destination.rt.width);           //result width
        cmd.SetComputeIntParam(JPEGComputeShader, "ResultHeight", destination.rt.height);         //result hight
        
        if(frameIndex == 0 || !Application.isPlaying){
            cmd.SetComputeIntParam(JPEGComputeShader, "IsIFrame", 1);
            frameIndex = Mathf.Max(numBFrames.value, 0);
        }
        else{
            cmd.SetComputeIntParam(JPEGComputeShader, "IsIFrame", 0);
            if(useIFrames.value) frameIndex--;
        }
        
        cmd.DispatchCompute(JPEGComputeShader, mainKernel,
            Mathf.CeilToInt((destination.rt.width + (xGroupSize-1)) / xGroupSize),
            Mathf.CeilToInt((destination.rt.height + (yGroupSize-1)) / yGroupSize),
            1);
        
        if (bypassMaterial != null)
        {
            cmd.Blit(destination, lastFrameIdentifier, bypassMaterial);
        }
    }

    public override void Cleanup()
    {
        if (motionFrame != null)
        {
            motionFrame.Release();
            motionFrame = null;
        }
        if (lastFrame != null)
        {
            lastFrame.Release();
            lastFrame = null;
        }
        if (motionMaterial != null)
        {
            UnityEngine.Object.DestroyImmediate(motionMaterial);
            motionMaterial = null;
        }
        if (bypassMaterial != null)
        {
            UnityEngine.Object.DestroyImmediate(bypassMaterial);
            bypassMaterial = null;
        }
    }
}