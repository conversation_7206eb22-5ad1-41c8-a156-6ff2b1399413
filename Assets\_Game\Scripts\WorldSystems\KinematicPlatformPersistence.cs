using System;
using UnityEngine;

[DisallowMultipleComponent]
public class KinematicPlatformPersistence : MonoBeh<PERSON>our
{
    [Header("Identity")]
    [SerializeField] private string objectName = "";
    [SerializeField] private string uniqueId = "";
    [SerializeField] private string category = "Platforms";

    [Header("References")]
    [SerializeField] private KinematicPlatform platform;

    [Header("Debug")]
    [SerializeField] private bool logSaves = false;

    private bool isRegistered;

    public string UniqueId => uniqueId;
    public string ObjectName => string.IsNullOrEmpty(objectName) ? gameObject.name : objectName;

    private void Reset()
    {
        platform = GetComponent<KinematicPlatform>();
    }

    private void Awake()
    {
        if (platform == null) platform = GetComponent<KinematicPlatform>();
        if (string.IsNullOrEmpty(uniqueId)) GenerateUniqueId();
    }

    private void Start()
    {
        RegisterWithPersistence();
    }

    private void OnEnable()
    {
        if (isRegistered) return;
        RegisterWithPersistence();
    }

    private void OnDisable()
    {
        if (!isRegistered) return;
        UnregisterWithPersistence();
    }

    private void OnDestroy()
    {
        if (isRegistered)
        {
            // Save final state before destruction
            var pm = PersistenceManager.Instance;
            if (pm != null)
            {
                pm.OnKinematicPlatformStateChanged(this);
            }
            UnregisterWithPersistence();
        }
    }

    private void GenerateUniqueId()
    {
        string sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        string baseName = ObjectName.Replace("(Clone)", string.Empty).Trim();
        Vector3 pos = transform.position;
        int px = Mathf.RoundToInt(pos.x * 100);
        int py = Mathf.RoundToInt(pos.y * 100);
        int pz = Mathf.RoundToInt(pos.z * 100);
        uniqueId = $"KP_{sceneName}_{baseName}_{px}_{py}_{pz}";
    }

    private void RegisterWithPersistence()
    {
        var pm = PersistenceManager.Instance;
        if (pm == null) return;
        pm.RegisterKinematicPlatform(this);
        isRegistered = true;
    }

    private void UnregisterWithPersistence()
    {
        var pm = PersistenceManager.Instance;
        if (pm == null) return;
        pm.UnregisterKinematicPlatform(this);
        isRegistered = false;
    }

    public PersistenceManager.KinematicPlatformData GetSaveData()
    {
        if (platform == null) platform = GetComponent<KinematicPlatform>();
        var state = platform != null ? platform.GetRuntimeState() : null;

        var data = new PersistenceManager.KinematicPlatformData
        {
            id = uniqueId,
            objectName = ObjectName,
            sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name,
            position = transform.position,
            rotation = transform.rotation,
            scale = transform.localScale,
            currentWaypointIndex = state?.currentWaypointIndex ?? 0,
            directionMultiplier = state?.directionMultiplier ?? 1,
            isMoving = state?.isMoving ?? false,
            isWaiting = state?.isWaiting ?? false,
            waitTimeRemaining = state?.waitTimeRemaining ?? 0f,
            currentTime = state?.currentTime ?? 0f,
            journeyDuration = state?.journeyDuration ?? 0f,
            journeyProgress = state?.journeyProgress ?? 0f,
            startPosition = state?.startPosition ?? transform.position,
            targetPosition = state?.targetPosition ?? transform.position,
            motionType = state != null ? (int)state.motionType : 0,
            curvePower = state?.curvePower ?? 2f,
            isLooping = state?.isLooping ?? true,
            moveSpeed = state?.moveSpeed ?? 5f,
            isActive = gameObject.activeSelf
        };

        if (logSaves)
        {
            Debug.Log($"[KinematicPlatformPersistence] Save {ObjectName} pos={data.position} moving={data.isMoving} wp={data.currentWaypointIndex} t={data.currentTime}/{data.journeyDuration}");
        }

        return data;
    }

    public void RestoreFromSaveData(PersistenceManager.KinematicPlatformData data)
    {
        if (data == null) return;

        // Base transform
        transform.position = data.position;
        transform.rotation = data.rotation;
        transform.localScale = data.scale;
        if (data.isActive && !gameObject.activeSelf)
        {
            gameObject.SetActive(true);
        }

        // Runtime state
        if (platform == null) platform = GetComponent<KinematicPlatform>();
        if (platform != null)
        {
            var state = new KinematicPlatform.KinematicPlatformState
            {
                currentWaypointIndex = data.currentWaypointIndex,
                directionMultiplier = data.directionMultiplier,
                isMoving = data.isMoving,
                isWaiting = data.isWaiting,
                waitTimeRemaining = data.waitTimeRemaining,
                currentTime = data.currentTime,
                journeyDuration = data.journeyDuration,
                journeyProgress = data.journeyProgress,
                startPosition = data.startPosition,
                targetPosition = data.targetPosition,
                motionType = (KinematicPlatform.MotionCurveType)data.motionType,
                curvePower = data.curvePower,
                isLooping = data.isLooping,
                moveSpeed = data.moveSpeed,
            };

            platform.ApplyRuntimeState(state, teleportToExactPosition: true);
        }
    }

    // Call this when you change the platform state at runtime (optional helper)
    public void NotifyChanged()
    {
        var pm = PersistenceManager.Instance;
        if (pm != null)
        {
            pm.OnKinematicPlatformStateChanged(this);
        }
    }
}


