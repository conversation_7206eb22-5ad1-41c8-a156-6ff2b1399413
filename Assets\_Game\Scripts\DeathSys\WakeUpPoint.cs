using UnityEngine;
using UnityEngine.Events;
using System;

public class WakeUpPoint : MonoBehavi<PERSON>
{
    [Head<PERSON>("Selection Settings")]
    [Tooltip("Base chance of this wake up point being selected")]
    [Range(0f, 100f)]
    public float baseSelectionChance = 10f;
    
    [Tooltip("Maximum distance at which this point has full selection weight")]
    public float optimalDistance = 50f;
    
    [Tooltip("Distance beyond which this point has minimum selection weight")]
    public float maxDistance = 200f;
    
    [<PERSON><PERSON>("Scenario Settings")]
    [Tooltip("Event triggered when player respawns at this wake up point")]
    public UnityEvent OnPlayerRespawn;
    
    [Tooltip("Optional custom scenario script reference")]
    public WakeUpScenario wakeUpScenario;

    /// <summary>
    /// Calculates a weight for selecting this wake up point based on distance from player
    /// </summary>
    public float CalculateSelectionWeight(Vector3 playerPosition)
    {
        float distance = Vector3.Distance(transform.position, playerPosition);
        
        // Distance factor (1.0 when close, reduces as distance increases)
        float distanceFactor = 1.0f;
        if (distance > optimalDistance)
        {
            distanceFactor = Mathf.Clamp01(1.0f - ((distance - optimalDistance) / (maxDistance - optimalDistance)));
        }
        
        // Final weight is base chance modified by distance
        return baseSelectionChance * distanceFactor;
    }

    /// <summary>
    /// Called when player respawns at this point
    /// </summary>
    public void OnRespawn(PlayerStatus playerStatus)
    {
        Debug.Log($"[WakeUpPoint] OnRespawn at '{name}' t={Time.time:F2}");
        // Trigger the Unity Event
        OnPlayerRespawn?.Invoke();
        
        // Execute scenario if available
        if (wakeUpScenario != null)
        {
            wakeUpScenario.ExecuteScenario(playerStatus);
        }
    }
}

/// <summary>
/// Base class for custom wake up scenarios
/// Inherit from this to create custom respawn behaviors
/// </summary>
public abstract class WakeUpScenario : MonoBehaviour
{
    /// <summary>
    /// Override this to implement custom scenario logic
    /// </summary>
    public abstract void ExecuteScenario(PlayerStatus playerStatus);
}