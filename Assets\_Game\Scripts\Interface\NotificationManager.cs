using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Collections.Generic;

public class NotificationManager : MonoBehaviour
{
    [System.Serializable]
    public class NotificationSettings
    {
        public float displayTime = 3f;
        public int maxNotifications = 5;
        public float animationDuration = 0.3f;
    }

    [SerializeField] private UIDocument uiDocument;
    [SerializeField] private NotificationSettings settings;
    [SerializeField] private VisualTreeAsset notificationTemplate;
    [SerializeField] private VisualTreeAsset batteryNotificationTemplate; // New battery template

    private VisualElement root;
    private VisualElement mainContainer;
    private List<VisualElement> activeNotifications = new List<VisualElement>();

    public static NotificationManager Instance { get; private set; }

    private void Awake()
    {
        // Just set the instance without any checks
        Instance = this;
    }

    private void OnEnable()
    {
        if (uiDocument == null || uiDocument.rootVisualElement == null)
        {
            Debug.LogError("UIDocument is not properly set up.");
            return;
        }

        // Ensure the UIDocument is active for runtime rendering
        if (!uiDocument.enabled)
        {
            uiDocument.enabled = true;
            Debug.Log("[NotificationManager] Enabled UIDocument for notifications");
        }

        root = uiDocument.rootVisualElement;
        mainContainer = root.Q<VisualElement>("AlertMainContainer");

        if (mainContainer == null)
        {
            Debug.LogError("AlertMainContainer not found in the UXML.");
        }
        else
        {
            // Ensure container is visible; avoid overriding project positioning/USS
            mainContainer.style.display = DisplayStyle.Flex;
        }

        // Optional debug
        // Debug.Log($"[NotificationManager] notificationTemplate assigned: {notificationTemplate != null}");
        // Debug.Log($"[NotificationManager] batteryNotificationTemplate assigned: {batteryNotificationTemplate != null}");
        // if (batteryNotificationTemplate != null)
        // {
        //     Debug.Log($"[NotificationManager] batteryNotificationTemplate name: {batteryNotificationTemplate.name}");
        // }
    }

    public void ShowNotification(string message)
    {
        if (mainContainer == null || notificationTemplate == null)
        {
            Debug.LogError("mainContainer or notificationTemplate is null");
            return;
        }

        VisualElement notification = notificationTemplate.Instantiate();
        notification.Q<Label>("Text").text = message;

        notification.AddToClassList("notification-enter");
        mainContainer.Add(notification);
        activeNotifications.Add(notification);

        if (activeNotifications.Count > settings.maxNotifications)
        {
            RemoveOldestNotification();
        }

        StartCoroutine(AnimateNotification(notification));
    }

    public VisualElement ShowBatteryNotification(float currentPercentage, System.Action<VisualElement, List<VisualElement>> onInitialized)
    {
        if (mainContainer == null || batteryNotificationTemplate == null)
        {
            Debug.LogError($"mainContainer is null: {mainContainer == null}, batteryNotificationTemplate is null: {batteryNotificationTemplate == null}");
            return null;
        }

        Debug.Log($"[NotificationManager] Creating battery notification from template: {batteryNotificationTemplate.name}");
        VisualElement notification = batteryNotificationTemplate.Instantiate();
        Debug.Log($"[NotificationManager] Instantiated notification: {notification?.name}, children: {notification?.childCount}");

        notification.AddToClassList("notification-enter");
        mainContainer.Add(notification);
        activeNotifications.Add(notification);

        if (activeNotifications.Count > settings.maxNotifications)
        {
            RemoveOldestNotification();
        }

        // Let the BatteryController initialize the battery display
        if (onInitialized != null)
        {
            // We need to wait a frame for the UI to be properly set up
            StartCoroutine(InitializeBatteryNotification(notification, currentPercentage, onInitialized));
        }

        StartCoroutine(AnimateNotification(notification));

        return notification;
    }

    private IEnumerator InitializeBatteryNotification(VisualElement notification, float currentPercentage, System.Action<VisualElement, List<VisualElement>> onInitialized)
    {
        yield return new WaitForEndOfFrame();
        
        // Fallback styles if template/USS didn't size the element
        bool needsFallback = notification.resolvedStyle.width <= 1f || notification.resolvedStyle.height <= 1f;
        if (needsFallback)
        {
            notification.style.display = DisplayStyle.Flex;
            notification.style.position = Position.Absolute;
            // Bottom-center positioning to match original UX
            notification.style.left = 0;
            notification.style.right = 0;
            notification.style.bottom = 24;
            notification.style.top = StyleKeyword.Null;
            notification.style.alignSelf = Align.Center;
            notification.style.flexDirection = FlexDirection.Row;
            notification.style.alignItems = Align.Center;
            notification.style.justifyContent = Justify.Center;
            notification.style.minWidth = 360;
            notification.style.minHeight = 64;
            notification.style.paddingLeft = 12;
            notification.style.paddingRight = 12;
            notification.style.paddingTop = 8;
            notification.style.paddingBottom = 8;
            notification.style.backgroundColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
            Debug.Log("[NotificationManager] Applied fallback layout/styles to battery notification");
        }

        // Just call the callback with the notification and an empty list
        // The BatteryController will handle creating the segments
        onInitialized?.Invoke(notification, new List<VisualElement>());
    }

    private IEnumerator AnimateNotification(VisualElement notification)
    {
        yield return new WaitForEndOfFrame();
        notification.RemoveFromClassList("notification-enter");
        notification.AddToClassList("notification-enter-active");

        yield return new WaitForSeconds(settings.displayTime);

        notification.RemoveFromClassList("notification-enter-active");
        notification.AddToClassList("notification-exit-active");

        yield return new WaitForSeconds(settings.animationDuration);

        if (notification.parent == mainContainer)
        {
            mainContainer.Remove(notification);
        }
        activeNotifications.Remove(notification);
    }

    private void RemoveOldestNotification()
    {
        if (activeNotifications.Count > 0 && mainContainer != null)
        {
            VisualElement oldestNotification = activeNotifications[0];
            mainContainer.Remove(oldestNotification);
            activeNotifications.RemoveAt(0);
        }
    }
}